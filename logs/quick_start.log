2025-08-05 10:17:15,534 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:17:15,541 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:17:15,542 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:17:15,543 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:17:15,545 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:17:15,569 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:17:15,571 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:17:15,573 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:17:15,574 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:17:15,582 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:17:15,583 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:17:15,657 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:17:15,660 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:17:15,669 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:17:15,672 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:17:15,676 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:17:15,678 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:17:15,679 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:17:15,687 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:17:15,690 - __main__ - ERROR - quick_start.py:306 - Demo failed: No processed data to save
2025-08-05 10:17:15,691 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:17:40,817 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:17:40,821 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:17:40,822 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:17:40,823 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:17:40,825 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:17:40,841 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:17:40,843 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:17:40,844 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:17:40,845 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:17:40,851 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:17:40,852 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:17:40,914 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:17:40,916 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:17:40,922 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:17:40,923 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:17:40,926 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:17:40,927 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:17:40,929 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:17:40,933 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:17:40,934 - __main__ - ERROR - quick_start.py:306 - Demo failed: No processed data to save
2025-08-05 10:17:40,936 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:18:35,455 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:18:35,459 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:18:35,460 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:18:35,462 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:18:35,463 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:18:35,478 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:18:35,480 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:18:35,481 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:18:35,483 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:18:35,488 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:18:35,489 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:18:35,548 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:18:35,549 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:18:35,555 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:18:35,556 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:18:35,559 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:18:35,561 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:18:35,562 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:18:35,566 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:18:35,568 - __main__ - ERROR - quick_start.py:306 - Demo failed: No processed data to save
2025-08-05 10:18:35,569 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:19:36,464 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:19:36,468 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:19:36,469 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:19:36,470 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:19:36,471 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:19:36,490 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:19:36,492 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:19:36,494 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:19:36,496 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:19:36,504 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:19:36,505 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:19:36,565 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:19:36,567 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:19:36,573 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:19:36,574 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:19:36,577 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:19:36,578 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:19:36,579 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:19:36,584 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:19:36,585 - __main__ - ERROR - quick_start.py:306 - Demo failed: No processed data to save
2025-08-05 10:19:36,587 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:26:14,602 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:26:14,606 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:26:14,607 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:26:14,609 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:26:14,610 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:26:14,626 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:26:14,628 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:26:14,629 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:26:14,631 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:26:14,636 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:26:14,637 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:26:14,697 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:26:14,698 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:26:14,704 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:26:14,705 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:26:14,708 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:26:14,710 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:26:14,711 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:26:14,715 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:26:14,717 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:26:14,720 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:26:14,721 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:26:14,723 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:26:14,724 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:26:14,725 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:26:14,730 - __main__ - ERROR - quick_start.py:306 - Demo failed: Missing key ray
    full_key: ray
    object_type=dict
2025-08-05 10:26:14,732 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:28:55,545 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:28:55,548 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:28:55,550 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:28:55,551 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:28:55,552 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:28:55,569 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:28:55,570 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:28:55,571 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:28:55,573 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:28:55,578 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:28:55,580 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:28:55,640 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:28:55,641 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:28:55,647 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:28:55,648 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:28:55,651 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:28:55,652 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:28:55,654 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:28:55,657 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:28:55,659 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:28:55,663 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:28:55,664 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:28:55,665 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:28:55,666 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:28:55,668 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:28:55,673 - __main__ - ERROR - quick_start.py:306 - Demo failed: Missing key ray
    full_key: training.ray
    object_type=dict
2025-08-05 10:28:55,674 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:31:10,225 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:31:10,228 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:31:10,230 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:31:10,231 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:31:10,232 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:31:10,248 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:31:10,250 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:31:10,251 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:31:10,252 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:31:10,258 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:31:10,259 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:31:10,319 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:31:10,321 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:31:10,326 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:31:10,328 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:31:10,331 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:31:10,332 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:31:10,333 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:31:10,337 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:31:10,339 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:31:10,342 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:31:10,344 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:31:10,345 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:31:10,346 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:31:10,347 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:31:10,352 - __main__ - ERROR - quick_start.py:306 - Demo failed: Missing key ray
    full_key: training.ray
    object_type=dict
2025-08-05 10:31:10,354 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:31:21,202 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:31:21,207 - __main__ - INFO - quick_start.py:269 - ============================================================
2025-08-05 10:31:21,208 - __main__ - INFO - quick_start.py:270 - RL-CT Framework Quick Start Demo
2025-08-05 10:31:21,209 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 10:31:21,210 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:31:21,230 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:31:21,231 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:31:21,233 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:31:21,234 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:31:21,240 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:31:21,242 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:31:21,301 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:31:21,302 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:31:21,308 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:31:21,310 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:31:21,313 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:31:21,314 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:31:21,315 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:31:21,319 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:31:21,321 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:31:21,325 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:31:21,326 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:31:21,328 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:31:21,329 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:31:21,330 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:31:21,338 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 10:31:21,340 - __main__ - INFO - quick_start.py:174 - Training config saved: configs/training/quick_start.yaml
2025-08-05 10:31:21,341 - __main__ - INFO - quick_start.py:180 - Starting model training...
2025-08-05 10:31:21,348 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 10:34:03,296 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:34:03,300 - __main__ - INFO - quick_start.py:269 - ============================================================
2025-08-05 10:34:03,301 - __main__ - INFO - quick_start.py:270 - RL-CT Framework Quick Start Demo
2025-08-05 10:34:03,302 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 10:34:03,304 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:34:03,320 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:34:03,321 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:34:03,323 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:34:03,324 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:34:03,330 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:34:03,331 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:34:03,390 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:34:03,391 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:34:03,397 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:34:03,398 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:34:03,401 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:34:03,403 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:34:03,404 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:34:03,408 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:34:03,410 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:34:03,413 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:34:03,415 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:34:03,416 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:34:03,417 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:34:03,418 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:34:03,426 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 10:34:03,427 - __main__ - INFO - quick_start.py:174 - Training config saved: configs/training/quick_start.yaml
2025-08-05 10:34:03,428 - __main__ - INFO - quick_start.py:180 - Starting model training...
2025-08-05 10:34:03,435 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
